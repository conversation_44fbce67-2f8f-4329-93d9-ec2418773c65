import express, { Request, Response } from 'express';
import cors from 'cors';
import path from 'path';
import dotenv from 'dotenv';

dotenv.config();

import authRoutes from './routes/auth';
import emailRoutes from './routes/email';
import ImapMonitorService from './services/imapMonitor';

const app = express();
const PORT = process.env.PORT || 3001;

app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, '../client/build')));

app.use('/api/auth', authRoutes);
app.use('/api/email', emailRoutes);

app.get('*', (req: Request, res: Response) => {
  res.sendFile(path.join(__dirname, '../client/build/index.html'));
});

// Initialize IMAP email monitor service
const imapMonitor = new ImapMonitorService();

app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  
  // Start IMAP monitoring after server starts
  imapMonitor.start();
});

// Graceful shutdown
const shutdown = () => {
  console.log('\nShutting down gracefully...');
  imapMonitor.stop();
  process.exit(0);
};

process.on('SIGINT', shutdown);
process.on('SIGTERM', shutdown);
process.on('SIGQUIT', shutdown);
process.on('SIGHUP', shutdown);

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  console.error('Uncaught Exception:', err);
  shutdown();
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  shutdown();
});