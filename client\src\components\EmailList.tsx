import React from 'react';
import { 
  List, 
  ListItem, 
  ListItemButton, 
  ListItemText, 
  ListItemIcon,
  Typography,
  Box,
  Chip,
  Card,
  CardContent
} from '@mui/material';
import { EmailListProps } from '../types';

const EmailList: React.FC<EmailListProps> = ({ messages, onSelectMessage, selectedMessageId }) => {
  if (!messages || messages.length === 0) {
    return (
      <Card>
        <CardContent sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="h2" sx={{ fontSize: '3rem', mb: 2 }}>📧</Typography>
          <Typography variant="h6" gutterBottom>No messages found</Typography>
          <Typography variant="body2" color="text.secondary">
            Your processed emails will appear here
          </Typography>
        </CardContent>
      </Card>
    );
  }

  const getPriorityColor = (subject: string) => {
    const lowerSubject = subject.toLowerCase();
    if (lowerSubject.includes('urgent') || lowerSubject.includes('cancelled')) {
      return 'error';
    }
    if (lowerSubject.includes('reminder') || lowerSubject.includes('due')) {
      return 'warning';
    }
    return 'success';
  };

  const getCategoryIcon = (subject: string): string => {
    const lowerSubject = subject.toLowerCase();
    if (lowerSubject.includes('school') || lowerSubject.includes('picture')) {
      return '🏫';
    }
    if (lowerSubject.includes('soccer') || lowerSubject.includes('sports')) {
      return '⚽';
    }
    if (lowerSubject.includes('medical') || lowerSubject.includes('health')) {
      return '🏥';
    }
    return '📧';
  };

  const getTags = (subject: string) => {
    const lowerSubject = subject.toLowerCase();
    const tags = [];
    
    if (lowerSubject.includes('urgent')) tags.push({ label: 'Urgent', color: 'error' });
    if (lowerSubject.includes('reminder')) tags.push({ label: 'Reminder', color: 'info' });
    if (lowerSubject.includes('cancelled')) tags.push({ label: 'Cancelled', color: 'warning' });
    
    return tags;
  };

  return (
    <Card>
      <List>
        {messages.map((message, index) => (
          <ListItem key={message.id} disablePadding divider={index < messages.length - 1}>
            <ListItemButton
              selected={selectedMessageId === message.id}
              onClick={() => onSelectMessage(message.id)}
            >
              <ListItemIcon>
                <Typography variant="h6">{getCategoryIcon(message.subject)}</Typography>
              </ListItemIcon>
              <ListItemText
                primary={
                  <Box>
                    <Typography variant="subtitle2" noWrap>
                      {message.subject}
                    </Typography>
                    <Box display="flex" justifyContent="space-between" alignItems="center" mt={0.5}>
                      <Typography variant="caption" color="text.secondary" noWrap sx={{ maxWidth: '60%' }}>
                        {message.from}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {message.date}
                      </Typography>
                    </Box>
                  </Box>
                }
                secondary={
                  <Box mt={1}>
                    <Typography variant="body2" color="text.secondary" sx={{ 
                      display: '-webkit-box',
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: 'vertical',
                      overflow: 'hidden'
                    }}>
                      {message.snippet}
                    </Typography>
                    <Box mt={1} display="flex" gap={0.5} flexWrap="wrap">
                      {getTags(message.subject).map((tag, tagIndex) => (
                        <Chip
                          key={tagIndex}
                          label={tag.label}
                          size="small"
                          color={tag.color as any}
                          variant="outlined"
                        />
                      ))}
                    </Box>
                  </Box>
                }
              />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
    </Card>
  );
};

export default EmailList;