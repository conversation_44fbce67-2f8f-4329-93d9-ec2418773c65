# Pigeon Squad (working title) - Family Activity Monitor

A minimal Node.js Express service with React frontend for monitoring family activities through email processing.

## Features

- Basic authentication (register/login)
- SQLite database with Prisma ORM
- Gmail integration for email processing
- Real-time email monitoring via IMAP
- React frontend dashboard
- JWT-based authentication

## Setup

1. Install dependencies:
```bash
npm install
cd client && npm install
```

2. Configure environment variables:
```bash
cp .env.example .env
# Edit .env with your Gmail API credentials and app name
```

3. Database Setup (Prisma + SQLite):
```bash
npm run db:migrate    # Create database and run migrations
npm run db:generate   # Generate Prisma client
```

4. Gmail IMAP Setup:
   - Enable 2-factor authentication on your Gmail account
   - Generate an App Password:
     - Go to [Google Account Settings](https://myaccount.google.com/)
     - Security > 2-Step Verification > App passwords
     - Generate password for "Mail"
     - Copy the 16-character password
   - Add to your .env file:
     ```
     GMAIL_EMAIL=<EMAIL>
     GMAIL_APP_PASSWORD=your-16-char-app-password
     ```

5. Run the application:
```bash
npm run dev
```

## Project Structure

```
sprout/
├── server/
│   ├── index.js          # Express server
│   ├── db.js             # Prisma database client
│   ├── routes/
│   │   ├── auth.js       # Authentication routes
│   │   └── email.js      # Email processing routes
│   ├── middleware/
│   │   └── auth.js       # JWT middleware
│   └── services/
│       └── gmail.js      # Gmail API service
├── prisma/
│   ├── schema.prisma     # Database schema
│   └── migrations/       # Database migrations
├── client/
│   └── src/
│       ├── App.js        # Main React app
│       └── components/   # React components
└── specs/                # Project specifications
```

## API Endpoints

- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/email/messages` - Fetch Gmail messages
- `GET /api/email/messages/:id` - Get specific message

## Database Commands

```bash
npm run db:migrate    # Run database migrations
npm run db:generate   # Generate Prisma client
prisma studio         # Open database browser
```

## Configuration

### App Name
The app name is configurable via environment variables:
- `APP_NAME` - Used by the server
- `REACT_APP_NAME` - Used by the React frontend

To change the app name, update these values in your `.env` file.

## Next Steps

- Implement AI processing pipeline
- Add notification system
- Enhance UI/UX
- Add family management features
- Migrate to PostgreSQL for production

