{"name": "pigeon-squad", "version": "1.0.0", "description": "Pigeon Squad - Family Activity Monitor", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "cross-env NODE_OPTIONS=--no-deprecation nodemon", "client": "cd client && npm start", "build": "npm run build:server && cd client && npm run build", "build:server": "cd server && tsc", "start": "node server/dist/index.js", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev"}, "dependencies": {"@prisma/client": "^6.16.1", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "imap": "^0.8.19", "jsonwebtoken": "^9.0.2", "mailparser": "^3.7.4", "nodemailer": "^6.9.7", "prisma": "^6.16.1"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/imap": "^0.8.42", "@types/jsonwebtoken": "^9.0.5", "@types/mailparser": "^3.4.6", "@types/node": "^20.10.5", "concurrently": "^8.2.2", "cross-env": "^10.0.0", "nodemon": "^3.0.1", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}