{"compilerOptions": {"target": "ES2020", "lib": ["dom", "dom.iterable", "ES2020"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "useDefineForClassFields": true, "noImplicitReturns": true, "noUncheckedIndexedAccess": false}, "include": ["src"], "exclude": ["node_modules"]}