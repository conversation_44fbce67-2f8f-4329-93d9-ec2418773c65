import { render, screen } from '@testing-library/react';
import App from './App';

test('renders Sprout landing page', () => {
  render(<App />);
  const titleElement = screen.getByRole('heading', { name: /Sprout/i, level: 1 });
  expect(titleElement).toBeInTheDocument();
});

test('renders family activity monitor tagline', () => {
  render(<App />);
  const taglineElement = screen.getByText(/Family Activity Monitor/i);
  expect(taglineElement).toBeInTheDocument();
});

test('renders get started button', () => {
  render(<App />);
  const buttonElement = screen.getByText(/Get Started/i);
  expect(buttonElement).toBeInTheDocument();
});

test('renders learn more button', () => {
  render(<App />);
  const learnMoreButton = screen.getByText(/Learn More/i);
  expect(learnMoreButton).toBeInTheDocument();
});
